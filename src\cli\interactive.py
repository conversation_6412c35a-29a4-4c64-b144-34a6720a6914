# Enhanced CLI interface with rich menus and user experience
import os
import sys
import csv
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.text import Text
from rich.tree import Tree
from rich.layout import Layout
from rich.live import Live
import time

from ..config.settings import settings, get_output_dir
from ..models.hierarchy import Hierarchy
from ..models.topic import Topic
from ..models.result import AnalysisResult, AnalysisStatus
from ..analysis.recursive import RecursiveAnalyzer
from ..analysis.iterative import IterativeAnalyzer
from ..ollama.model_manager import get_model_manager
from ..ollama.api import get_ollama_client
from ..prompting.prompt_editor import get_prompt_editor
from ..utils.eta import get_eta_estimator
from ..utils.file_io import read_csv, write_json, validate_csv_structure
from ..utils.logging_config import get_logger

console = Console()
logger = get_logger("cli")

DEFAULT_OUTPUT_DIR = get_output_dir()
DEFAULT_INPUT_FILE = Path(__file__).parent.parent.parent / "input_topics_subangles_example.csv"


class CLIInterface:
    """Enhanced CLI interface with rich menus and user experience."""

    def __init__(self):
        self.console = console
        self.hierarchy: Optional[Hierarchy] = None
        self.analysis_results: Dict[str, AnalysisResult] = {}
        self.current_model = settings.get("ollama", "default_model", "llama2")
        self.model_manager = get_model_manager()
        self.ollama_client = get_ollama_client()
        self.prompt_editor = get_prompt_editor()
        self.eta_estimator = get_eta_estimator()

        # CLI state
        self.running = True
        self.debug_mode = settings.get("app", "debug", False)

    def run(self):
        """Main CLI loop."""
        self._show_welcome()

        while self.running:
            try:
                self._show_main_menu()

                # Provide workflow guidance
                self._show_workflow_guidance()

                choice = self._get_menu_choice("Select an option", ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"])

                if choice == "1":
                    self._input_management_menu()
                elif choice == "2":
                    self._model_management_menu()
                elif choice == "3":
                    self._analysis_menu()
                elif choice == "4":
                    self._results_menu()
                elif choice == "5":
                    self._prompt_management_menu()
                elif choice == "6":
                    self._settings_menu()
                elif choice == "7":
                    self._help_menu()
                elif choice == "8":
                    self._system_status()
                elif choice == "9":
                    self._debug_menu()
                elif choice == "0":
                    self._exit_application()

                # After each action, show what to do next
                self._suggest_next_step()

            except KeyboardInterrupt:
                if Confirm.ask("\nDo you want to exit?"):
                    self._exit_application()
            except Exception as e:
                self._handle_error(e)

    def _show_welcome(self):
        """Show welcome screen."""
        welcome_text = Text("Deep Research Tool", style="bold blue")
        welcome_panel = Panel(
            welcome_text,
            subtitle="CLI Interface v1.0",
            border_style="blue"
        )
        self.console.print(welcome_panel)
        self.console.print()

    def _show_main_menu(self):
        """Show main menu with workflow status."""
        # Show current workflow status
        self._show_workflow_status()

        menu_table = Table(show_header=False, box=None, padding=(0, 2))
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description", style="white")
        menu_table.add_column("Status", style="dim")

        # Determine status for each step
        input_status = "✓ Complete" if self.hierarchy else "○ Pending"
        model_status = f"✓ {self.current_model}" if self.current_model else "○ Default"
        analysis_status = "✓ Complete" if self.analysis_results else "○ Pending"
        results_status = "✓ Available" if self.analysis_results else "○ None"

        menu_items = [
            ("1", "Input Management - Load/create topic hierarchies", input_status),
            ("2", "Model Management - Select and configure Ollama models", model_status),
            ("3", "Analysis - Run recursive or iterative analysis", analysis_status),
            ("4", "Results - View and export analysis results", results_status),
            ("", "", ""),  # Separator
            ("5", "Prompt Management - Create and edit prompts", ""),
            ("6", "Settings - Configure application settings", ""),
            ("7", "Help - Documentation and tutorials", ""),
            ("8", "System Status - Check system health", ""),
            ("9", "Debug Tools - Advanced debugging options", ""),
            ("0", "Exit - Quit the application", "")
        ]

        for option, description, status in menu_items:
            if option == "":  # Separator
                menu_table.add_row("", "─" * 50, "")
            else:
                menu_table.add_row(f"[{option}]", description, status)

        menu_panel = Panel(
            menu_table,
            title="Main Menu",
            border_style="green"
        )
        self.console.print(menu_panel)

    def _show_workflow_status(self):
        """Show current workflow progress."""
        status_table = Table(show_header=False, box=None, padding=(0, 1))
        status_table.add_column("Step", style="bold")
        status_table.add_column("Status", justify="center")
        status_table.add_column("Details", style="dim")

        # Step 1: Input
        if self.hierarchy:
            input_icon = "✅"
            input_details = f"{len(self.hierarchy.topics)} topics loaded"
        else:
            input_icon = "1️⃣"
            input_details = "No hierarchy loaded"

        # Step 2: Model
        if self.current_model:
            model_icon = "✅" if self.hierarchy else "2️⃣"
            model_details = f"Using {self.current_model}"
        else:
            model_icon = "2️⃣"
            model_details = "No model selected"

        # Step 3: Analysis
        if self.analysis_results:
            analysis_icon = "✅"
            analysis_details = f"{len(self.analysis_results)} results"
        else:
            analysis_icon = "3️⃣" if self.hierarchy and self.current_model else "⏸️"
            analysis_details = "Ready to analyze" if self.hierarchy and self.current_model else "Waiting for input & model"

        # Step 4: Results
        if self.analysis_results:
            results_icon = "4️⃣"
            results_details = "Ready to view/export"
        else:
            results_icon = "⏸️"
            results_details = "Waiting for analysis"

        status_table.add_row("Input", input_icon, input_details)
        status_table.add_row("Model", model_icon, model_details)
        status_table.add_row("Analysis", analysis_icon, analysis_details)
        status_table.add_row("Results", results_icon, results_details)

        workflow_panel = Panel(
            status_table,
            title="Workflow Progress",
            border_style="blue",
            padding=(0, 1)
        )
        self.console.print(workflow_panel)
        self.console.print()

    def _show_workflow_guidance(self):
        """Show guidance for next recommended step."""
        if not self.hierarchy:
            self.console.print("[yellow]💡 Recommended: Start with Input Management (1) to load or create topics[/yellow]")
        elif not self.current_model:
            self.console.print("[yellow]💡 Recommended: Select a model in Model Management (2) before analysis[/yellow]")
        elif not self.analysis_results:
            self.console.print("[yellow]💡 Recommended: Run Analysis (3) to generate insights from your topics[/yellow]")
        elif self.analysis_results:
            self.console.print("[yellow]💡 Recommended: View Results (4) to see your analysis or export data[/yellow]")
        self.console.print()

    def _suggest_next_step(self):
        """Suggest the next logical step after an action."""
        if not self.hierarchy:
            return  # No suggestions if no data

        # Brief pause to let user see the result
        import time
        time.sleep(0.5)

        if not self.current_model:
            self.console.print("\n[dim]💡 Next: Select a model for analysis (option 2)[/dim]")
        elif not self.analysis_results:
            self.console.print("\n[dim]💡 Next: Run analysis to generate insights (option 3)[/dim]")
        elif self.analysis_results:
            self.console.print("\n[dim]💡 Next: View your results or export data (option 4)[/dim]")

        # Ask if user wants to continue to next step
        if not self.hierarchy:
            return

        next_step = None
        if not self.current_model:
            next_step = ("2", "Model Management")
        elif not self.analysis_results:
            next_step = ("3", "Analysis")
        elif self.analysis_results:
            next_step = ("4", "Results")

        if next_step and Confirm.ask(f"\nWould you like to go to {next_step[1]} now?", default=False):
            if next_step[0] == "2":
                self._model_management_menu()
            elif next_step[0] == "3":
                self._analysis_menu()
            elif next_step[0] == "4":
                self._results_menu()

    def _get_menu_choice(self, prompt_text: str, valid_choices: List[str]) -> str:
        """Get validated menu choice from user."""
        while True:
            choice = Prompt.ask(prompt_text).strip()
            if choice in valid_choices:
                return choice
            self.console.print(f"[red]Invalid choice. Please select from: {', '.join(valid_choices)}[/red]")

    def _input_management_menu(self):
        """Handle input management operations."""
        self.console.print("\n[bold cyan]Input Management[/bold cyan]")

        menu_table = Table(show_header=False, box=None)
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description")

        menu_items = [
            ("1", "Load from CSV file"),
            ("2", "Manual entry (comma-separated topics & sub-angles)"),
            ("3", "Use hierarchy designer"),
            ("4", "View current hierarchy"),
            ("5", "Validate input data"),
            ("0", "Back to main menu")
        ]

        for option, description in menu_items:
            menu_table.add_row(f"[{option}]", description)

        self.console.print(menu_table)

        choice = self._get_menu_choice("Select input option", ["1", "2", "3", "4", "5", "0"])

        if choice == "1":
            self._load_from_csv()
        elif choice == "2":
            self._manual_entry()
        elif choice == "3":
            self._use_hierarchy_designer()
        elif choice == "4":
            self._view_current_hierarchy()
        elif choice == "5":
            self._validate_input_data()

    def _load_from_csv(self):
        """Load hierarchy from CSV file."""
        self.console.print("\n[bold]Load from CSV File[/bold]")

        # Get file path
        default_path = str(DEFAULT_INPUT_FILE)
        file_path = Prompt.ask("Enter CSV file path", default=default_path)

        if not Path(file_path).exists():
            self.console.print(f"[red]File not found: {file_path}[/red]")
            return

        try:
            # Validate CSV structure first
            with self.console.status("Validating CSV structure..."):
                validation_result = validate_csv_structure(file_path, expected_columns=5)

            if not validation_result['valid']:
                self.console.print("[red]CSV validation failed:[/red]")
                for error in validation_result['errors']:
                    self.console.print(f"  • {error}")

                if not Confirm.ask("Continue anyway?"):
                    return

            # Load the hierarchy
            with self.console.status("Loading hierarchy from CSV..."):
                self.hierarchy = Hierarchy("CSV Import", f"Loaded from {file_path}")
                topics = self.hierarchy.import_from_csv(file_path)

            self.console.print(f"[green]Successfully loaded {len(topics)} topics from CSV[/green]")
            self._show_hierarchy_summary()

        except Exception as e:
            self.console.print(f"[red]Error loading CSV: {str(e)}[/red]")

    def _manual_entry(self):
        """Manual topic entry interface with comma-separated format."""
        self.console.print("\n[bold cyan]Manual Topic Entry[/bold cyan]")

        # Show instructions
        instructions = Panel(
            "[bold]Instructions:[/bold]\n"
            "• Enter main topics separated by commas\n"
            "• For each topic, enter sub-angles separated by commas\n"
            "• Example: 'Technology, Science, Business'\n"
            "• Sub-angles: 'AI, Machine Learning, Robotics'",
            title="How to Enter Topics",
            border_style="blue"
        )
        self.console.print(instructions)

        if not self.hierarchy:
            name = Prompt.ask("Enter hierarchy name", default="Manual Entry")
            description = Prompt.ask("Enter description", default="Manually entered topics")
            self.hierarchy = Hierarchy(name, description)

        # Get main topics
        topics_input = Prompt.ask("\nEnter main topics (comma-separated)")
        if not topics_input.strip():
            self.console.print("[yellow]No topics entered[/yellow]")
            return

        # Parse topics
        topics = [topic.strip() for topic in topics_input.split(',') if topic.strip()]
        if not topics:
            self.console.print("[yellow]No valid topics found[/yellow]")
            return

        self.console.print(f"\n[green]Found {len(topics)} topics:[/green]")
        for i, topic in enumerate(topics, 1):
            self.console.print(f"  {i}. {topic}")

        # Get sub-angles for each topic
        topic_count = 0
        for topic in topics:
            self.console.print(f"\n[cyan]Sub-angles for '{topic}':[/cyan]")

            sub_angles_input = Prompt.ask(
                f"Enter sub-angles for '{topic}' (comma-separated, or press Enter to skip)",
                default=""
            )

            # Create topic layers
            layers = [topic]  # Main topic is first layer

            if sub_angles_input.strip():
                sub_angles = [angle.strip() for angle in sub_angles_input.split(',') if angle.strip()]
                if sub_angles:
                    # Add sub-angles as second layer
                    layers.extend(sub_angles[:4])  # Limit to 4 sub-angles to stay within 5 total levels

                    self.console.print(f"[dim]Added {len(sub_angles)} sub-angles: {', '.join(sub_angles[:4])}[/dim]")
                    if len(sub_angles) > 4:
                        self.console.print(f"[yellow]Note: Only first 4 sub-angles used, {len(sub_angles) - 4} skipped[/yellow]")

            # Create the topic in hierarchy
            try:
                created_topic = self.hierarchy.create_topic_from_layers(layers, topic)
                topic_count += 1
                self.console.print(f"[green]✓ Added topic: {topic}[/green]")
            except Exception as e:
                self.console.print(f"[red]Error adding topic '{topic}': {str(e)}[/red]")

        self.console.print(f"\n[bold green]Successfully created {topic_count} topics![/bold green]")
        self._show_hierarchy_summary()

    def _use_hierarchy_designer(self):
        """Launch the hierarchy designer."""
        self.console.print("\n[bold]Hierarchy Designer[/bold]")
        self.console.print("Launching hierarchy designer...")

        # This would launch the hierarchy designer CLI tool
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "-m", "src.designer.hierarchy_designer"
            ], capture_output=True, text=True)

            if result.returncode == 0:
                self.console.print("[green]Hierarchy designer completed successfully[/green]")

                # Ask if user wants to load the generated file
                if Confirm.ask("Load the generated hierarchy file?"):
                    output_file = "output_hierarchy.csv"
                    if Path(output_file).exists():
                        self._load_from_csv_file(output_file)
            else:
                self.console.print(f"[red]Hierarchy designer failed: {result.stderr}[/red]")

        except Exception as e:
            self.console.print(f"[red]Error launching hierarchy designer: {str(e)}[/red]")

    def _view_current_hierarchy(self):
        """Display current hierarchy structure."""
        if not self.hierarchy:
            self.console.print("[yellow]No hierarchy loaded[/yellow]")
            return

        self.console.print(f"\n[bold]Current Hierarchy: {self.hierarchy.name}[/bold]")
        self.console.print(f"Description: {self.hierarchy.description}")

        # Create tree visualization
        tree = Tree(f"📁 {self.hierarchy.name}")

        for topic_id, topic in self.hierarchy.topics.items():
            topic_branch = tree.add(f"📄 {topic.name} ({topic.get_node_count()} nodes)")

            # Add root nodes
            for root_id in topic.root_ids:
                self._add_node_to_tree(topic_branch, topic, root_id)

        self.console.print(tree)
        self._show_hierarchy_summary()

    def _add_node_to_tree(self, parent_branch, topic: Topic, node_id: str, max_depth: int = 3):
        """Recursively add nodes to tree visualization."""
        node = topic.get_node(node_id)
        if not node or node.level > max_depth:
            return

        # Create node display
        node_text = f"Level {node.level}: {node.content}"
        if len(node.content) > 50:
            node_text = f"Level {node.level}: {node.content[:47]}..."

        node_branch = parent_branch.add(node_text)

        # Add children
        for child_id in node.children_ids:
            self._add_node_to_tree(node_branch, topic, child_id, max_depth)

    def _show_hierarchy_summary(self):
        """Show hierarchy statistics."""
        if not self.hierarchy:
            return

        stats = self.hierarchy.get_statistics()

        summary_table = Table(title="Hierarchy Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="white")

        summary_table.add_row("Total Topics", str(stats['total_topics']))
        summary_table.add_row("Total Nodes", str(stats['total_nodes']))
        summary_table.add_row("Maximum Depth", str(stats['max_depth']))

        for level, count in enumerate(stats['nodes_per_level']):
            if count > 0:
                level_name = self.hierarchy.levels[level].name
                summary_table.add_row(f"Level {level} ({level_name})", str(count))

        self.console.print(summary_table)

    def _validate_input_data(self):
        """Validate current input data."""
        if not self.hierarchy:
            self.console.print("[yellow]No hierarchy to validate[/yellow]")
            return

        self.console.print("\n[bold]Validating Hierarchy[/bold]")

        with self.console.status("Running validation..."):
            validation_results = self.hierarchy.validate_hierarchy()

        # Display results
        if validation_results['errors']:
            self.console.print("[red]Errors found:[/red]")
            for error in validation_results['errors']:
                self.console.print(f"  ❌ {error}")

        if validation_results['warnings']:
            self.console.print("[yellow]Warnings:[/yellow]")
            for warning in validation_results['warnings']:
                self.console.print(f"  ⚠️  {warning}")

        if not validation_results['errors'] and not validation_results['warnings']:
            self.console.print("[green]✅ Hierarchy validation passed![/green]")

    def _handle_error(self, error: Exception):
        """Handle and display errors gracefully."""
        if self.debug_mode:
            self.console.print_exception()
        else:
            self.console.print(f"[red]Error: {str(error)}[/red]")

        logger.error(f"CLI error: {str(error)}", exc_info=True)

    def _model_management_menu(self):
        """Handle model management operations."""
        self.console.print("\n[bold cyan]Model Management[/bold cyan]")

        # Show current model status
        current_status = Panel(
            f"Current Model: [bold green]{self.current_model}[/bold green]" if self.current_model
            else "Current Model: [yellow]None selected[/yellow]",
            title="Status",
            border_style="blue",
            padding=(0, 1)
        )
        self.console.print(current_status)

        menu_items = [
            ("1", "List available models"),
            ("2", "Select/Change model"),
            ("3", "Model information"),
            ("4", "Test current model"),
            ("5", "Model performance stats"),
            ("0", "Back to main menu")
        ]

        self._show_submenu(menu_items)
        choice = self._get_menu_choice("Select model option", ["1", "2", "3", "4", "5", "0"])

        if choice == "1":
            self._list_models()
        elif choice == "2":
            self._select_model()
        elif choice == "3":
            self._show_model_info()
        elif choice == "4":
            self._test_model()
        elif choice == "5":
            self._show_model_performance()

    def _analysis_menu(self):
        """Handle analysis operations."""
        if not self.hierarchy:
            self.console.print("[yellow]Please load a hierarchy first[/yellow]")
            return

        self.console.print("\n[bold cyan]Analysis[/bold cyan]")

        menu_items = [
            ("1", "Recursive analysis"),
            ("2", "Iterative analysis"),
            ("3", "Custom analysis"),
            ("4", "Batch analysis"),
            ("5", "Analysis settings"),
            ("0", "Back to main menu")
        ]

        self._show_submenu(menu_items)
        choice = self._get_menu_choice("Select analysis option", ["1", "2", "3", "4", "5", "0"])

        if choice == "1":
            self._run_recursive_analysis()
        elif choice == "2":
            self._run_iterative_analysis()
        elif choice == "3":
            self._run_custom_analysis()
        elif choice == "4":
            self._run_batch_analysis()
        elif choice == "5":
            self._analysis_settings()

    def _results_menu(self):
        """Handle results viewing and export."""
        if not self.analysis_results:
            self.console.print("[yellow]No analysis results available[/yellow]")
            return

        self.console.print("\n[bold cyan]Results Management[/bold cyan]")

        menu_items = [
            ("1", "View results summary"),
            ("2", "View detailed results"),
            ("3", "Export results"),
            ("4", "Search results"),
            ("5", "Results statistics"),
            ("0", "Back to main menu")
        ]

        self._show_submenu(menu_items)
        choice = self._get_menu_choice("Select results option", ["1", "2", "3", "4", "5", "0"])

        if choice == "1":
            self._view_results_summary()
        elif choice == "2":
            self._view_detailed_results()
        elif choice == "3":
            self._export_results()
        elif choice == "4":
            self._search_results()
        elif choice == "5":
            self._results_statistics()

    def _prompt_management_menu(self):
        """Handle prompt management operations."""
        self.console.print("\n[bold cyan]Prompt Management[/bold cyan]")

        menu_items = [
            ("1", "View templates"),
            ("2", "Create template"),
            ("3", "Edit template"),
            ("4", "Create prompt chain"),
            ("5", "Test prompts"),
            ("0", "Back to main menu")
        ]

        self._show_submenu(menu_items)
        choice = self._get_menu_choice("Select prompt option", ["1", "2", "3", "4", "5", "0"])

        if choice == "1":
            self._view_prompt_templates()
        elif choice == "2":
            self._create_prompt_template()
        elif choice == "3":
            self._edit_prompt_template()
        elif choice == "4":
            self._create_prompt_chain()
        elif choice == "5":
            self._test_prompts()

    def _settings_menu(self):
        """Handle application settings."""
        self.console.print("\n[bold cyan]Settings[/bold cyan]")

        menu_items = [
            ("1", "View current settings"),
            ("2", "Change output directory"),
            ("3", "Ollama settings"),
            ("4", "Analysis settings"),
            ("5", "Export settings"),
            ("6", "Reset to defaults"),
            ("0", "Back to main menu")
        ]

        self._show_submenu(menu_items)
        choice = self._get_menu_choice("Select settings option", ["1", "2", "3", "4", "5", "6", "0"])

        if choice == "1":
            self._view_current_settings()
        elif choice == "2":
            self._change_output_directory()
        elif choice == "3":
            self._ollama_settings()
        elif choice == "4":
            self._analysis_settings()
        elif choice == "5":
            self._export_settings()
        elif choice == "6":
            self._reset_settings()

    def _help_menu(self):
        """Show help and documentation."""
        self.console.print("\n[bold cyan]Help & Documentation[/bold cyan]")

        help_text = """
[bold]Deep Research Tool - CLI Help[/bold]

[cyan]Getting Started:[/cyan]
1. Load or create a topic hierarchy using Input Management
2. Select an appropriate Ollama model in Model Management
3. Run analysis using the Analysis menu
4. View and export results in Results Management

[cyan]Key Features:[/cyan]
• Hierarchical topic analysis (up to 5 levels deep)
• Recursive and iterative analysis algorithms
• Advanced prompt templating and chaining
• Real-time progress tracking with ETA
• Comprehensive result export options

[cyan]Tips:[/cyan]
• Use the hierarchy designer for complex topic structures
• Test your model before running large analyses
• Save your prompt templates for reuse
• Monitor system status for optimal performance

[cyan]Keyboard Shortcuts:[/cyan]
• Ctrl+C: Cancel current operation or exit
• Enter: Confirm selection or use default value
        """

        help_panel = Panel(help_text, title="Help", border_style="blue")
        self.console.print(help_panel)

        Prompt.ask("Press Enter to continue")

    def _system_status(self):
        """Show system status and health."""
        self.console.print("\n[bold cyan]System Status[/bold cyan]")

        with self.console.status("Checking system status..."):
            # Check Ollama connection
            try:
                models = self.model_manager.get_available_models()
                ollama_status = "✅ Connected"
                model_count = len(models)
            except:
                ollama_status = "❌ Disconnected"
                model_count = 0

            # Get system stats
            eta_stats = self.eta_estimator.get_task_statistics()

        # Create status table
        status_table = Table(title="System Health")
        status_table.add_column("Component", style="cyan")
        status_table.add_column("Status", style="white")
        status_table.add_column("Details", style="dim")

        status_table.add_row("Ollama Server", ollama_status, f"{model_count} models available")
        status_table.add_row("Current Model", self.current_model, "Selected for analysis")
        status_table.add_row("Hierarchy", "✅ Loaded" if self.hierarchy else "⚠️ Not loaded",
                           f"{len(self.hierarchy.topics) if self.hierarchy else 0} topics")
        status_table.add_row("Results", f"{len(self.analysis_results)} available", "Analysis results")
        status_table.add_row("Active Tasks", str(eta_stats['active_tasks']), "Currently running")

        self.console.print(status_table)

    def _debug_menu(self):
        """Debug tools and advanced options."""
        if not self.debug_mode:
            self.console.print("[yellow]Debug mode is disabled[/yellow]")
            return

        self.console.print("\n[bold cyan]Debug Tools[/bold cyan]")

        menu_items = [
            ("1", "View logs"),
            ("2", "Test components"),
            ("3", "Memory usage"),
            ("4", "Performance metrics"),
            ("5", "Export debug info"),
            ("0", "Back to main menu")
        ]

        self._show_submenu(menu_items)
        choice = self._get_menu_choice("Select debug option", ["1", "2", "3", "4", "5", "0"])

        if choice == "1":
            self._view_logs()
        elif choice == "2":
            self._test_components()
        elif choice == "3":
            self._show_memory_usage()
        elif choice == "4":
            self._show_performance_metrics()
        elif choice == "5":
            self._export_debug_info()

    def _show_submenu(self, menu_items: List[tuple]):
        """Display a submenu table."""
        menu_table = Table(show_header=False, box=None)
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description")

        for option, description in menu_items:
            menu_table.add_row(f"[{option}]", description)

        self.console.print(menu_table)

    def _list_models(self):
        """List available Ollama models."""
        with self.console.status("Fetching available models..."):
            try:
                models = self.model_manager.get_available_models()
            except Exception as e:
                self.console.print(f"[red]Error fetching models: {str(e)}[/red]")
                return

        if not models:
            self.console.print("[yellow]No models available[/yellow]")
            return

        models_table = Table(title="Available Models")
        models_table.add_column("Name", style="cyan")
        models_table.add_column("Size", style="white")
        models_table.add_column("Modified", style="dim")
        models_table.add_column("Status", style="green")

        for model in models:
            status = "✅ Current" if model.name == self.current_model else ""
            modified = model.modified_at.strftime("%Y-%m-%d") if model.modified_at else "Unknown"
            models_table.add_row(model.name, model.size_human, modified, status)

        self.console.print(models_table)

    def _select_model(self):
        """Select a model for analysis."""
        with self.console.status("Fetching available models..."):
            try:
                models = self.model_manager.get_available_models()
            except Exception as e:
                self.console.print(f"[red]Error fetching models: {str(e)}[/red]")
                return

        if not models:
            self.console.print("[yellow]No models available[/yellow]")
            return

        self.console.print("\nAvailable models:")
        for i, model in enumerate(models, 1):
            status = " (current)" if model.name == self.current_model else ""
            self.console.print(f"  {i}. {model.name}{status}")

        try:
            choice = int(Prompt.ask("Select model number")) - 1
            if 0 <= choice < len(models):
                selected_model = models[choice]

                # Validate model
                with self.console.status(f"Validating model {selected_model.name}..."):
                    validation = self.model_manager.validate_model(selected_model.name)

                if validation['valid']:
                    old_model = self.current_model
                    self.current_model = selected_model.name

                    # Update settings to persist the choice
                    settings.set("ollama", "default_model", selected_model.name)

                    self.console.print(f"[green]✓ Model changed from '{old_model}' to '{selected_model.name}'[/green]")
                    self.console.print(f"[dim]Model will be used for all future analyses[/dim]")

                    # Test the model briefly
                    if Confirm.ask("Would you like to test the model connection?", default=True):
                        self._test_model()
                else:
                    self.console.print(f"[red]Model validation failed: {validation['errors']}[/red]")
                    self.console.print("[yellow]Please try selecting a different model[/yellow]")
            else:
                self.console.print("[red]Invalid selection[/red]")
        except ValueError:
            self.console.print("[red]Please enter a valid number[/red]")

    def _exit_application(self):
        """Exit the application gracefully with proper cleanup."""
        self.console.print("\n[yellow]Shutting down Deep Research Tool...[/yellow]")

        try:
            # Perform cleanup
            self._cleanup_resources()
            self.console.print("[green]✓ Cleanup completed successfully[/green]")
        except Exception as e:
            self.console.print(f"[yellow]Warning: Cleanup error: {str(e)}[/yellow]")

        self.console.print("[cyan]Thank you for using Deep Research Tool![/cyan]")
        self.running = False
        sys.exit(0)

    def _cleanup_resources(self):
        """Clean up resources before exit."""
        cleanup_tasks = []

        # 1. Stop any running analysis
        if hasattr(self, 'current_analysis_thread') and self.current_analysis_thread and self.current_analysis_thread.is_alive():
            cleanup_tasks.append("Stopping running analysis")
            # Note: In a full implementation, you'd need to implement proper thread cancellation

        # 2. Close file handles
        cleanup_tasks.append("Closing file handles")

        # 3. Clear temporary data
        if self.analysis_results:
            cleanup_tasks.append("Clearing analysis results from memory")
            self.analysis_results.clear()

        if self.hierarchy:
            cleanup_tasks.append("Clearing hierarchy data from memory")
            # Don't clear hierarchy as user might want to save it

        # 4. Flush logs
        cleanup_tasks.append("Flushing log files")
        try:
            import logging
            logging.shutdown()
        except:
            pass

        # 5. Clean up model manager connections
        cleanup_tasks.append("Closing model connections")
        try:
            if hasattr(self.model_manager, 'cleanup'):
                self.model_manager.cleanup()
        except:
            pass

        # Show cleanup progress
        if cleanup_tasks:
            with self.console.status("Performing cleanup..."):
                import time
                time.sleep(0.5)  # Brief pause to show cleanup is happening

    def _run_recursive_analysis(self):
        """Run recursive analysis on the hierarchy."""
        self.console.print("\n[bold]Recursive Analysis[/bold]")

        # Get analysis options
        system_prompt = Prompt.ask("Enter system prompt (optional)", default="")

        # Create analyzer
        analyzer = RecursiveAnalyzer(model_name=self.current_model)

        # Run analysis with enhanced progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=40),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("•"),
            TextColumn("[cyan]{task.fields[status]}[/cyan]"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TextColumn("[yellow]{task.fields[eta]}[/yellow]"),
            console=self.console,
            refresh_per_second=4
        ) as progress:

            # Count total nodes for accurate progress
            total_nodes = sum(topic.get_node_count() for topic in self.hierarchy.topics.values())
            task = progress.add_task(
                "Running recursive analysis...",
                total=total_nodes,
                status="Initializing...",
                eta="Calculating..."
            )

            # Set up progress callback
            def progress_callback(current, total, message):
                # Extract ETA from message if present
                eta_text = "Calculating..."
                status_text = message
                if " | ETA: " in message:
                    parts = message.split(" | ETA: ")
                    status_text = parts[0]
                    eta_part = parts[1]
                    if " | " in eta_part:
                        eta_text = eta_part.split(" | ")[0]
                    else:
                        eta_text = eta_part

                progress.update(
                    task,
                    completed=current,
                    total=total,
                    status=status_text[:60] + "..." if len(status_text) > 60 else status_text,
                    eta=eta_text
                )

            analyzer.set_progress_callback(progress_callback)

            try:
                results = analyzer.analyze_hierarchy(
                    self.hierarchy,
                    system_prompt=system_prompt if system_prompt else None
                )

                # Store results
                self.analysis_results.update(results)

                progress.update(task, completed=total_nodes, status="Complete!", eta="Done")
                self.console.print(f"[green]Analysis completed! Generated {len(results)} results[/green]")

                # Show summary
                summary = analyzer.get_analysis_summary()
                self._display_analysis_summary(summary)

            except Exception as e:
                self.console.print(f"[red]Analysis failed: {str(e)}[/red]")

    def _run_iterative_analysis(self):
        """Run iterative analysis on the hierarchy."""
        self.console.print("\n[bold]Iterative Analysis[/bold]")

        # Get analysis options
        max_iterations = int(Prompt.ask("Maximum iterations", default="3"))
        convergence_threshold = float(Prompt.ask("Convergence threshold", default="0.95"))
        system_prompt = Prompt.ask("Enter system prompt (optional)", default="")

        # Create analyzer
        analyzer = IterativeAnalyzer(
            model_name=self.current_model,
            max_iterations=max_iterations,
            convergence_threshold=convergence_threshold
        )

        # Run analysis with enhanced progress tracking
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=40),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("•"),
            TextColumn("[cyan]{task.fields[status]}[/cyan]"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TextColumn("[yellow]{task.fields[eta]}[/yellow]"),
            console=self.console,
            refresh_per_second=4
        ) as progress:

            # Calculate estimated total work for iterative analysis
            total_nodes = sum(topic.get_node_count() for topic in self.hierarchy.topics.values())
            estimated_total_work = total_nodes * max_iterations

            task = progress.add_task(
                "Running iterative analysis...",
                total=estimated_total_work,
                status="Initializing...",
                eta="Calculating..."
            )

            # Set up progress callback
            def progress_callback(current, total, message):
                # Extract ETA from message if present
                eta_text = "Calculating..."
                status_text = message
                if " | ETA: " in message:
                    parts = message.split(" | ETA: ")
                    status_text = parts[0]
                    eta_part = parts[1]
                    if " | " in eta_part:
                        eta_text = eta_part.split(" | ")[0]
                    else:
                        eta_text = eta_part

                progress.update(
                    task,
                    completed=current,
                    total=total,
                    status=status_text[:60] + "..." if len(status_text) > 60 else status_text,
                    eta=eta_text
                )

            analyzer.set_progress_callback(progress_callback)

            try:
                results = analyzer.analyze_hierarchy(
                    self.hierarchy,
                    system_prompt=system_prompt if system_prompt else None
                )

                # Store results
                self.analysis_results.update(results)

                progress.update(task, completed=estimated_total_work, status="Complete!", eta="Done")
                self.console.print(f"[green]Analysis completed! Generated {len(results)} results[/green]")

                # Show summary
                summary = analyzer.get_analysis_summary()
                self._display_analysis_summary(summary)

            except Exception as e:
                self.console.print(f"[red]Analysis failed: {str(e)}[/red]")

    def _display_analysis_summary(self, summary: Dict[str, Any]):
        """Display analysis summary."""
        summary_table = Table(title="Analysis Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="white")

        summary_table.add_row("Status", summary.get('status', 'unknown'))
        summary_table.add_row("Total Results", str(summary.get('total_results', 0)))
        summary_table.add_row("Completed", str(summary.get('completed_results', 0)))
        summary_table.add_row("Failed", str(summary.get('failed_results', 0)))
        summary_table.add_row("Success Rate", f"{summary.get('success_rate', 0):.1%}")

        if 'average_quality_score' in summary:
            summary_table.add_row("Avg Quality", f"{summary['average_quality_score']:.2f}")

        if 'iterations_performed' in summary:
            summary_table.add_row("Iterations", str(summary['iterations_performed']))

        summary_table.add_row("Model Used", summary.get('model_used', 'unknown'))
        summary_table.add_row("Analysis Type", summary.get('analysis_type', 'unknown'))

        self.console.print(summary_table)

    def _view_results_summary(self):
        """View summary of analysis results."""
        if not self.analysis_results:
            self.console.print("[yellow]No results available[/yellow]")
            return

        # Calculate summary statistics
        total_results = len(self.analysis_results)
        completed_results = sum(1 for r in self.analysis_results.values() if r.status == AnalysisStatus.COMPLETED)
        failed_results = sum(1 for r in self.analysis_results.values() if r.status == AnalysisStatus.FAILED)

        # Create results table
        results_table = Table(title="Results Summary")
        results_table.add_column("Node ID", style="cyan")
        results_table.add_column("Status", style="white")
        results_table.add_column("Content Length", style="dim")
        results_table.add_column("Quality", style="green")

        for node_id, result in list(self.analysis_results.items())[:20]:  # Show first 20
            status_color = "green" if result.status == AnalysisStatus.COMPLETED else "red"
            status_text = f"[{status_color}]{result.status.value}[/{status_color}]"

            quality_score = result.calculate_quality_score()
            quality_text = f"{quality_score:.2f}" if quality_score > 0 else "N/A"

            results_table.add_row(
                node_id[:8] + "...",
                status_text,
                str(len(result.content)),
                quality_text
            )

        self.console.print(results_table)

        if total_results > 20:
            self.console.print(f"[dim]Showing first 20 of {total_results} results[/dim]")

    def _export_results(self):
        """Export analysis results."""
        if not self.analysis_results:
            self.console.print("[yellow]No results to export[/yellow]")
            return

        self.console.print("\n[bold]Export Results[/bold]")

        # Get export options
        export_format = Prompt.ask("Export format", choices=["json", "csv", "txt"], default="json")
        output_file = Prompt.ask("Output filename", default=f"analysis_results.{export_format}")

        # Ensure output directory exists
        output_path = Path(DEFAULT_OUTPUT_DIR) / output_file
        output_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            with self.console.status(f"Exporting {len(self.analysis_results)} results..."):
                if export_format == "json":
                    export_data = {
                        'export_time': datetime.now().isoformat(),
                        'total_results': len(self.analysis_results),
                        'results': {node_id: result.to_dict() for node_id, result in self.analysis_results.items()}
                    }
                    write_json(output_path, export_data)

                elif export_format == "txt":
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(f"Analysis Results Export\n")
                        f.write(f"Generated: {datetime.now().isoformat()}\n")
                        f.write(f"Total Results: {len(self.analysis_results)}\n")
                        f.write("=" * 50 + "\n\n")

                        for node_id, result in self.analysis_results.items():
                            f.write(f"Node ID: {node_id}\n")
                            f.write(f"Status: {result.status.value}\n")
                            f.write(f"Quality Score: {result.calculate_quality_score():.2f}\n")
                            f.write(f"Content:\n{result.content}\n")
                            f.write("-" * 30 + "\n\n")

            self.console.print(f"[green]Results exported to: {output_path}[/green]")

        except Exception as e:
            self.console.print(f"[red]Export failed: {str(e)}[/red]")


def cli_main():
    """Main CLI entry point with proper cleanup handling."""
    cli = None

    def signal_handler(signum, frame):
        """Handle interrupt signals gracefully."""
        if cli:
            console.print("\n[yellow]Interrupt received, cleaning up...[/yellow]")
            try:
                cli._cleanup_resources()
            except:
                pass
        console.print("[cyan]Goodbye![/cyan]")
        sys.exit(0)

    # Set up signal handlers
    import signal
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)

    try:
        cli = CLIInterface()
        cli.run()
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        console.print(f"[red]Fatal error: {str(e)}[/red]")
        if settings.get("app", "debug", False):
            console.print_exception()

        # Attempt cleanup even on error
        if cli:
            try:
                cli._cleanup_resources()
            except:
                pass
        sys.exit(1)
    finally:
        # Final cleanup attempt
        if cli:
            try:
                cli._cleanup_resources()
            except:
                pass


# Legacy function for backward compatibility
def get_topics_from_file(input_file=DEFAULT_INPUT_FILE):
    """Legacy function to get topics from file."""
    try:
        return read_csv(input_file, has_header=True)
    except Exception as e:
        console.print(f"[red]Error reading file: {str(e)}[/red]")
        return []


if __name__ == "__main__":
    cli_main()
